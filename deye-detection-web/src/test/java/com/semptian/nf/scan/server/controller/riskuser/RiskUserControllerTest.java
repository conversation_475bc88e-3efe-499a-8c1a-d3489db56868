package com.semptian.nf.scan.server.controller.riskuser;

import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.semptian.nf.scan.server.DetectionWebApp;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.boot.test.context.SpringBootTest;


/**
 * <AUTHOR>
 * Date:2023/2/20
 * Description  风险用户接口测试
 **/
@Slf4j
@SpringBootTest(classes = DetectionWebApp.class)
public class RiskUserControllerTest {
    private static final String URL_PREFIX = "http://127.0.0.1:8068/security_detection/";
    private static final String URL_RISK_USER_LIST = URL_PREFIX + "risk_user/risk_user_list.json";
    private static final String URL_RISK_USER_DETAIL_TOP = URL_PREFIX + "risk_user/risk_user_detail_top.json";
    private static final String URL_RISK_USER_DETAIL_DOWN = URL_PREFIX + "risk_user/risk_user_detail_down.json";

    @Test
    public void riskUserList() {
        String sortField = "sortJson=%7B\"threatCount\":0%7D";
        // 简单测试
        System.out.println(HttpUtil.createPost(URL_RISK_USER_LIST + "?timeType=1").execute());
        // 排序测试
        System.out.println(HttpUtil.createPost(URL_RISK_USER_LIST + "?timeType=1" + "&&" + sortField).execute());
        // 用户过滤
        System.out.println(HttpUtil.createPost(URL_RISK_USER_LIST + "?timeType=2&&userName=whitelistuser").execute());

    }

    @Test
    public void riskUserDetailTop() {
        // 简单测试
        System.out.println( HttpUtil.createGet(URL_RISK_USER_DETAIL_TOP + "?timeType=2&&userName=whitelistuser481&&sourceIp=***********").execute());
        // 点击top5测试
        System.out.println( HttpUtil.createGet(URL_RISK_USER_DETAIL_TOP + "?timeType=2&&userName=whitelistuser481&&sourceIp=***********").execute());
    }

    @Test
    public void riskUserDetailDown() {
        // 简单测试
        HttpResponse httpResponse = HttpUtil.createGet(URL_RISK_USER_DETAIL_DOWN + "?timeType=1&&userName=whitelistuser481&&sourceIp=***********").execute();
        System.out.println(httpResponse.body());
    }
}